"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Settings,
  Plus,
  FolderOpen,
  Shield,
  QrCode,
  Crown,
  ShieldCheck,
  Bell,
  Archive,
  FileText,
  ChevronRight,
  Layers,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarSeparator,
} from "@/components/ui/sidebar";

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  adminOnly?: boolean;
}

const sidebarItems: SidebarItem[] = [
  {
    title: "Home",
    href: "/",
    icon: Home,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
];

// Core admin items (non-dropdown)
const adminItems: SidebarItem[] = [
  {
    title: "Admin Dashboard",
    href: "/admin",
    icon: Shield,
  },
  {
    title: "Notifications",
    href: "/notifications",
    icon: Bell,
  },
  {
    title: "Documents",
    href: "/documents",
    icon: FileText,
  },
  {
    title: "Archives",
    href: "/archives",
    icon: Archive,
  },
  {
    title: "Generate QR Code",
    href: "/qr-code",
    icon: QrCode,
  },
];

// Template management items (for dropdown)
const templateItems: SidebarItem[] = [
  {
    title: "Add Template",
    href: "/templates/add",
    icon: Plus,
  },
  {
    title: "Manage Templates",
    href: "/templates/manage",
    icon: FolderOpen,
  },
];

export function AppSidebar() {
  const pathname = usePathname();
  const [adminMode, setAdminMode] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);

    const checkAdminMode = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";
        setAdminMode(savedAdminMode);
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("adminModeChanged", handleAdminModeChange);
      return () => {
        window.removeEventListener("adminModeChanged", handleAdminModeChange);
      };
    }
  }, []);

  // Don't render during SSR
  if (!isHydrated) {
    return null;
  }

  const isActive = (href: string) => {
    return pathname === href || (href !== "/" && pathname.startsWith(href));
  };

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader />

      <SidebarContent className="overflow-hidden">
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild isActive={isActive(item.href)}>
                      <Link href={item.href}>
                        <Icon className="h-4 w-4" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Admin Navigation - Only show if admin mode is active */}
        {adminMode && (
          <>
            <SidebarSeparator className="my-2 opacity-50" />
            <SidebarGroup>
              <SidebarGroupLabel>Administration</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {adminItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <SidebarMenuItem key={item.href}>
                        <SidebarMenuButton
                          asChild
                          isActive={isActive(item.href)}
                        >
                          <Link href={item.href}>
                            <Icon className="h-4 w-4" />
                            <span>{item.title}</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    );
                  })}

                  {/* Template Management Dropdown */}
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      className="w-full"
                      isActive={pathname.startsWith("/templates")}
                    >
                      <Layers className="h-4 w-4" />
                      <span>Templates</span>
                      <ChevronRight className="ml-auto h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
                    </SidebarMenuButton>
                    <SidebarMenuSub>
                      {templateItems.map((item) => {
                        const Icon = item.icon;
                        return (
                          <SidebarMenuSubItem key={item.href}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={isActive(item.href)}
                            >
                              <Link href={item.href}>
                                <Icon className="h-4 w-4" />
                                <span>{item.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        );
                      })}
                    </SidebarMenuSub>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}
      </SidebarContent>

      <SidebarFooter className="mt-auto">
        {adminMode && (
          <div className="flex items-center justify-center gap-1 px-4 py-2 text-xs text-muted-foreground border-t border-opacity-30">
            <Crown className="h-3 w-3 text-amber-500" />
            <ShieldCheck className="h-3 w-3 text-green-500" />
            <span className="group-data-[collapsible=icon]:hidden ml-1">
              Admin Mode
            </span>
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
