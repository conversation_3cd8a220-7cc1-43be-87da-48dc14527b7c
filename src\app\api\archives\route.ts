import { NextRequest, NextResponse } from 'next/server';
import { searchArchives, getArchiveFilters, ArchiveSearchParams } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check if this is a request for filters
    if (searchParams.get('filters') === 'true') {
      const filters = await getArchiveFilters();
      return NextResponse.json(filters);
    }

    // Parse search parameters
    const params: ArchiveSearchParams = {
      search: searchParams.get('search') || undefined,
      template_name: searchParams.get('template_name') && searchParams.get('template_name') !== '' ? searchParams.get('template_name') || undefined : undefined,
      barangay: searchParams.get('barangay') && searchParams.get('barangay') !== '' ? searchParams.get('barangay') || undefined : undefined,
      sort_by: (searchParams.get('sort_by') as any) || 'approved_at',
      sort_order: (searchParams.get('sort_order') as any) || 'desc',
      limit: parseInt(searchParams.get('limit') || '50'),
      offset: parseInt(searchParams.get('offset') || '0')
    };

    const result = await searchArchives(params);
    
    return NextResponse.json({
      success: true,
      data: result.documents,
      total: result.total,
      pagination: {
        limit: params.limit,
        offset: params.offset,
        hasMore: (params.offset || 0) + (params.limit || 50) < result.total
      }
    });

  } catch (error) {
    console.error('Archives API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch archives' },
      { status: 500 }
    );
  }
}
