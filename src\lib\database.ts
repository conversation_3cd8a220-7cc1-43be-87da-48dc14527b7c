import { generateUUID } from './utils';

// Server-side only imports
let Database: any;
let path: any;
let fs: any;

// Only import on server side
if (typeof window === 'undefined') {
  Database = require('better-sqlite3');
  path = require('path');
  fs = require('fs').promises;
}

// Database file path
const DB_PATH = typeof window === 'undefined' ? path?.join(process.cwd(), 'data', 'ldis.db') : '';

let db: any = null;

// Ensure data directory exists
const ensureDataDir = async (): Promise<void> => {
  const dataDir = path.dirname(DB_PATH);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
};

// Initialize database connection
export const initDatabase = async (): Promise<any> => {
  // Only run on server side
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (db) {
    return db;
  }

  await ensureDataDir();

  db = new Database(DB_PATH);

  // Enable WAL mode for better performance
  db.pragma('journal_mode = WAL');

  // Additional performance optimizations
  db.pragma('synchronous = NORMAL');
  db.pragma('cache_size = 10000');
  db.pragma('temp_store = MEMORY');
  db.pragma('mmap_size = 268435456'); // 256MB

  // Create tables if they don't exist
  await createTables();

  return db;
};

// Get database instance
export const getDatabase = async (): Promise<any> => {
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (!db) {
    return await initDatabase();
  }
  return db;
};

// Create database tables
const createTables = async (): Promise<void> => {
  if (!db) return;

  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      recovery_options TEXT, -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Templates table
  db.exec(`
    CREATE TABLE IF NOT EXISTS templates (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      filename TEXT NOT NULL,
      placeholders TEXT, -- JSON array
      layout_size TEXT DEFAULT 'A4',
      has_applicant_photo BOOLEAN DEFAULT FALSE,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Documents table (for generated documents)
  db.exec(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      template_id TEXT NOT NULL,
      document_data TEXT, -- JSON string of form data
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (template_id) REFERENCES templates (id)
    )
  `);

  // Check if notifications table needs migration (remove old columns)
  const tableInfo = db.prepare("PRAGMA table_info(notifications)").all() as any[];
  const hasOldColumns = tableInfo.some((col: any) => col.name === 'user_id' || col.name === 'pdf_file_path');

  if (hasOldColumns) {
    console.log('Migrating notifications table to remove unused columns...');

    // Backup existing data
    const existingData = db.prepare('SELECT id, title, message, type, is_read, created_at, updated_at, pdf_filename, pdf_data FROM notifications').all();

    // Drop old table
    db.exec('DROP TABLE IF EXISTS notifications');

    // Create new table with clean schema
    db.exec(`
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        pdf_data TEXT, -- JSON string of embedded PDF data
        user_id INTEGER REFERENCES users(id) -- Track which user/admin performed actions
      )
    `);

    // Restore data
    if (existingData.length > 0) {
      const insertStmt = db.prepare(`
        INSERT INTO notifications (id, title, message, type, is_read, created_at, updated_at, pdf_filename, pdf_data, user_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const row of existingData) {
        insertStmt.run(
          row.id,
          row.title,
          row.message,
          row.type,
          row.is_read,
          row.created_at,
          row.updated_at,
          row.pdf_filename,
          row.pdf_data,
          null // user_id will be null for existing records
        );
      }
      console.log(`Migrated ${existingData.length} notification records`);
    }
  } else {
    // Create table with new schema if it doesn't exist
    db.exec(`
      CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        pdf_data TEXT, -- JSON string of embedded PDF data
        user_id INTEGER REFERENCES users(id) -- Track which user/admin performed actions
      )
    `);
  }

  // Archives table for approved documents
  db.exec(`
    CREATE TABLE IF NOT EXISTS archives (
      id TEXT PRIMARY KEY,
      template_name TEXT NOT NULL,
      applicant_name TEXT NOT NULL,
      age INTEGER,
      barangay TEXT,
      ctc_number TEXT,
      day TEXT,
      first_name TEXT,
      last_name TEXT,
      middle_initial TEXT,
      month TEXT,
      or_number TEXT,
      suffix TEXT,
      tin_number TEXT,
      year TEXT,
      photo_path TEXT,
      pdf_path TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      approved_by TEXT,
      original_notification_id TEXT,
      metadata TEXT -- JSON string of additional metadata
    )
  `);

  // QR codes table for approved documents
  db.exec(`
    CREATE TABLE IF NOT EXISTS qr_codes (
      id TEXT PRIMARY KEY,
      document_id TEXT NOT NULL,
      document_type TEXT NOT NULL CHECK (document_type IN ('notification', 'archive')),
      qr_code_url TEXT NOT NULL,
      validation_url TEXT NOT NULL,
      network_ip TEXT,
      size_dimensions TEXT DEFAULT '150x150',
      display_dimensions TEXT DEFAULT '60x60',
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      expires_at DATETIME,
      is_active BOOLEAN DEFAULT TRUE,
      scan_count INTEGER DEFAULT 0,
      last_scanned_at DATETIME,
      created_by TEXT,
      FOREIGN KEY (document_id) REFERENCES notifications(id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
    CREATE INDEX IF NOT EXISTS idx_documents_template_id ON documents(template_id);
    CREATE INDEX IF NOT EXISTS idx_documents_generated_at ON documents(generated_at);
    CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
    CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
    CREATE INDEX IF NOT EXISTS idx_archives_applicant_name ON archives(applicant_name);
    CREATE INDEX IF NOT EXISTS idx_archives_template_name ON archives(template_name);
    CREATE INDEX IF NOT EXISTS idx_archives_approved_at ON archives(approved_at);
    CREATE INDEX IF NOT EXISTS idx_archives_barangay ON archives(barangay);
    CREATE INDEX IF NOT EXISTS idx_archives_ctc_number ON archives(ctc_number);
    CREATE INDEX IF NOT EXISTS idx_qr_codes_document_id ON qr_codes(document_id);
    CREATE INDEX IF NOT EXISTS idx_qr_codes_document_type ON qr_codes(document_type);
    CREATE INDEX IF NOT EXISTS idx_qr_codes_is_active ON qr_codes(is_active);
    CREATE INDEX IF NOT EXISTS idx_qr_codes_generated_at ON qr_codes(generated_at);
  `);
};

// Close database connection
export const closeDatabase = (): void => {
  if (db) {
    db.close();
    db = null;
  }
};

// Utility function to run migrations
export const runMigrations = async (): Promise<void> => {
  const database = await getDatabase();
  
  // Create migrations table if it doesn't exist
  database.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add any future migrations here
  const migrations: Array<{name: string; sql: string}> = [
    {
      name: '001_add_user_id_to_templates',
      sql: 'ALTER TABLE templates ADD COLUMN user_id INTEGER REFERENCES users(id)'
    },
    {
      name: '002_add_user_id_to_documents',
      sql: 'ALTER TABLE documents ADD COLUMN user_id INTEGER REFERENCES users(id)'
    },
    {
      name: '003_add_user_id_to_archives',
      sql: 'ALTER TABLE archives ADD COLUMN user_id INTEGER REFERENCES users(id)'
    },
    {
      name: '004_add_user_id_indexes',
      sql: `
        CREATE INDEX IF NOT EXISTS idx_templates_user_id ON templates(user_id);
        CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
        CREATE INDEX IF NOT EXISTS idx_archives_user_id ON archives(user_id);
      `
    },
    {
      name: '005_remove_invalid_notification_indexes',
      sql: `
        DROP INDEX IF EXISTS idx_notifications_user_id;
      `
    },
    {
      name: '006_replace_admin_id_with_user_id_in_notifications',
      sql: `
        -- Add user_id column to notifications table
        ALTER TABLE notifications ADD COLUMN user_id INTEGER REFERENCES users(id);

        -- Copy admin_id values to user_id (if needed for data preservation)
        -- UPDATE notifications SET user_id = CAST(admin_id AS INTEGER) WHERE admin_id IS NOT NULL AND admin_id != '';

        -- Create index for user_id
        CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
      `
    }
  ];

  for (const migration of migrations) {
    const existing = database.prepare('SELECT name FROM migrations WHERE name = ?').get(migration.name);

    if (!existing) {
      database.exec(migration.sql);
      database.prepare('INSERT INTO migrations (name) VALUES (?)').run(migration.name);
      console.log(`Migration ${migration.name} executed successfully`);
    }
  }
};

// Database maintenance function
export const optimizeDatabase = async (): Promise<void> => {
  const database = await getDatabase();

  try {
    console.log('Starting database optimization...');

    // Clean up orphaned documents (documents without valid templates)
    const orphanedDocs = database.prepare(`
      DELETE FROM documents
      WHERE template_id NOT IN (SELECT id FROM templates)
    `).run();

    if (orphanedDocs.changes > 0) {
      console.log(`Cleaned up ${orphanedDocs.changes} orphaned documents`);
    }

    // Clean up orphaned archives (archives without valid notification references)
    const orphanedArchives = database.prepare(`
      DELETE FROM archives
      WHERE original_notification_id IS NOT NULL
      AND original_notification_id NOT IN (SELECT id FROM notifications)
    `).run();

    if (orphanedArchives.changes > 0) {
      console.log(`Cleaned up ${orphanedArchives.changes} orphaned archives`);
    }

    // Vacuum the database to reclaim space
    database.exec('VACUUM');

    // Update statistics for query optimization
    database.exec('ANALYZE');

    console.log('Database optimization completed successfully');
  } catch (error) {
    console.error('Database optimization failed:', error);
    throw error;
  }
};

// Archive-related interfaces and functions
export interface ArchivedDocument {
  id: string;
  template_name: string;
  applicant_name: string;
  age?: number;
  barangay?: string;
  ctc_number?: string;
  day?: string;
  first_name?: string;
  last_name?: string;
  middle_initial?: string;
  month?: string;
  or_number?: string;
  suffix?: string;
  tin_number?: string;
  year?: string;
  photo_path?: string;
  pdf_path?: string;
  created_at: string;
  approved_at: string;
  approved_by?: string;
  original_notification_id?: string;
  metadata?: string;
}

export interface ArchiveSearchParams {
  search?: string;
  template_name?: string;
  barangay?: string;
  sort_by?: 'approved_at' | 'applicant_name' | 'template_name';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Archive a document from notification
export const archiveDocument = async (notificationData: any, approvedBy?: string): Promise<ArchivedDocument> => {
  const database = await getDatabase();

  const stmt = database.prepare(`
    INSERT INTO archives (
      id, template_name, applicant_name, age, barangay, ctc_number,
      day, first_name, last_name, middle_initial, month, or_number,
      suffix, tin_number, year, photo_path, pdf_path, approved_by,
      original_notification_id, metadata
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const archivedDoc: ArchivedDocument = {
    id: notificationData.id,
    template_name: notificationData.templateName || '',
    applicant_name: notificationData.applicantName || [
      notificationData.firstName || '',
      notificationData.middleInitial ? `${notificationData.middleInitial}.` : '',
      notificationData.lastName || ''
    ].filter(name => name.trim()).join(' ').trim() || 'Unknown Applicant',
    age: notificationData.age ? parseInt(notificationData.age) : undefined,
    barangay: notificationData.barangay,
    ctc_number: notificationData.ctcNumber,
    day: notificationData.day,
    first_name: notificationData.firstName,
    last_name: notificationData.lastName,
    middle_initial: notificationData.middleInitial,
    month: notificationData.month,
    or_number: notificationData.orNumber,
    suffix: notificationData.suffix,
    tin_number: notificationData.tinNumber,
    year: notificationData.year,
    photo_path: notificationData.photoPath,
    pdf_path: notificationData.pdfPath,
    created_at: notificationData.createdAt || new Date().toISOString(),
    approved_at: new Date().toISOString(),
    approved_by: approvedBy,
    original_notification_id: notificationData.id,
    metadata: JSON.stringify({
      ...notificationData,
      // Store the complete PDF data for reconstruction
      pdfData: notificationData.pdfData || null,
      templateId: notificationData.templateId || notificationData.id
    })
  };

  stmt.run(
    archivedDoc.id,
    archivedDoc.template_name,
    archivedDoc.applicant_name,
    archivedDoc.age,
    archivedDoc.barangay,
    archivedDoc.ctc_number,
    archivedDoc.day,
    archivedDoc.first_name,
    archivedDoc.last_name,
    archivedDoc.middle_initial,
    archivedDoc.month,
    archivedDoc.or_number,
    archivedDoc.suffix,
    archivedDoc.tin_number,
    archivedDoc.year,
    archivedDoc.photo_path,
    archivedDoc.pdf_path,
    archivedDoc.approved_by,
    archivedDoc.original_notification_id,
    archivedDoc.metadata
  );

  return archivedDoc;
};

// Search archived documents
export const searchArchives = async (params: ArchiveSearchParams = {}): Promise<{
  documents: ArchivedDocument[];
  total: number;
}> => {
  const database = await getDatabase();

  const {
    search = '',
    template_name,
    barangay,
    sort_by = 'approved_at',
    sort_order = 'desc',
    limit = 50,
    offset = 0
  } = params;

  let whereClause = 'WHERE 1=1';
  const queryParams: any[] = [];

  // Search in applicant name, template name, or barangay
  if (search) {
    whereClause += ` AND (
      applicant_name LIKE ? OR
      first_name LIKE ? OR
      last_name LIKE ? OR
      middle_initial LIKE ? OR
      template_name LIKE ? OR
      barangay LIKE ? OR
      ctc_number LIKE ? OR
      or_number LIKE ?
    )`;
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
  }

  if (template_name) {
    whereClause += ' AND template_name = ?';
    queryParams.push(template_name);
  }

  if (barangay) {
    whereClause += ' AND barangay = ?';
    queryParams.push(barangay);
  }

  const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;

  // Get total count
  const countStmt = database.prepare(`SELECT COUNT(*) as count FROM archives ${whereClause}`);
  const { count: total } = countStmt.get(...queryParams) as { count: number };

  // Get documents
  const stmt = database.prepare(`
    SELECT * FROM archives
    ${whereClause}
    ${orderClause}
    LIMIT ? OFFSET ?
  `);

  const documents = stmt.all(...queryParams, limit, offset) as ArchivedDocument[];

  return { documents, total };
};

// Get unique values for filters
export const getArchiveFilters = async () => {
  const database = await getDatabase();

  const templateNames = database.prepare('SELECT DISTINCT template_name FROM archives WHERE template_name IS NOT NULL ORDER BY template_name').all() as { template_name: string }[];

  return {
    templateNames: templateNames.map(t => t.template_name)
  };
};

// Get archived document by ID
export const getArchivedDocument = async (id: string): Promise<ArchivedDocument | null> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM archives WHERE id = ?');
  return stmt.get(id) as ArchivedDocument | null;
};

// Delete archived document
export const deleteArchivedDocument = async (id: string): Promise<boolean> => {
  const database = await getDatabase();
  const stmt = database.prepare('DELETE FROM archives WHERE id = ?');
  const result = stmt.run(id);
  return result.changes > 0;
};

// QR Code-related interfaces and functions
export interface QRCodeRecord {
  id: string;
  document_id: string;
  document_type: 'notification' | 'archive';
  qr_code_url: string;
  validation_url: string;
  network_ip?: string;
  size_dimensions: string;
  display_dimensions: string;
  generated_at: string;
  expires_at?: string;
  is_active: boolean;
  scan_count: number;
  last_scanned_at?: string;
  created_by?: string;
}

export interface CreateQRCodeData {
  document_id: string;
  document_type: 'notification' | 'archive';
  qr_code_url: string;
  validation_url: string;
  network_ip?: string;
  size_dimensions?: string;
  display_dimensions?: string;
  expires_at?: string;
  created_by?: string;
}

// Create QR code record
export const createQRCode = async (qrData: CreateQRCodeData): Promise<QRCodeRecord> => {
  const database = await getDatabase();
  const id = generateUUID();

  const stmt = database.prepare(`
    INSERT INTO qr_codes (
      id, document_id, document_type, qr_code_url, validation_url, network_ip,
      size_dimensions, display_dimensions, expires_at, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  stmt.run(
    id,
    qrData.document_id,
    qrData.document_type,
    qrData.qr_code_url,
    qrData.validation_url,
    qrData.network_ip || null,
    qrData.size_dimensions || '150x150',
    qrData.display_dimensions || '60x60',
    qrData.expires_at || null,
    qrData.created_by || null
  );

  return {
    id,
    ...qrData,
    size_dimensions: qrData.size_dimensions || '150x150',
    display_dimensions: qrData.display_dimensions || '60x60',
    generated_at: new Date().toISOString(),
    is_active: true,
    scan_count: 0
  };
};

// Get QR code by document ID
export const getQRCodeByDocumentId = async (documentId: string, documentType: 'notification' | 'archive'): Promise<QRCodeRecord | null> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM qr_codes WHERE document_id = ? AND document_type = ? AND is_active = 1 ORDER BY generated_at DESC LIMIT 1');
  return stmt.get(documentId, documentType) as QRCodeRecord | null;
};

// Get QR code by ID
export const getQRCodeById = async (id: string): Promise<QRCodeRecord | null> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM qr_codes WHERE id = ?');
  return stmt.get(id) as QRCodeRecord | null;
};

// Update QR code scan count
export const incrementQRCodeScanCount = async (documentId: string, documentType: 'notification' | 'archive'): Promise<boolean> => {
  const database = await getDatabase();
  const stmt = database.prepare(`
    UPDATE qr_codes
    SET scan_count = scan_count + 1, last_scanned_at = CURRENT_TIMESTAMP
    WHERE document_id = ? AND document_type = ? AND is_active = 1
  `);
  const result = stmt.run(documentId, documentType);
  return result.changes > 0;
};

// Deactivate QR code
export const deactivateQRCode = async (documentId: string, documentType: 'notification' | 'archive'): Promise<boolean> => {
  const database = await getDatabase();
  const stmt = database.prepare('UPDATE qr_codes SET is_active = 0 WHERE document_id = ? AND document_type = ?');
  const result = stmt.run(documentId, documentType);
  return result.changes > 0;
};

// Get all QR codes for a document
export const getQRCodesByDocumentId = async (documentId: string, documentType: 'notification' | 'archive'): Promise<QRCodeRecord[]> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM qr_codes WHERE document_id = ? AND document_type = ? ORDER BY generated_at DESC');
  return stmt.all(documentId, documentType) as QRCodeRecord[];
};


