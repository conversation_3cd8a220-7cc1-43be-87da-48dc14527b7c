// This is the old sidebar component - renamed to avoid conflicts
// The new Shadcn sidebar is now in app-sidebar.tsx
"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Settings,
  FileText,
  Plus,
  FolderOpen,
  Bell,
  Shield,
  QrCode,
  Menu,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  adminOnly?: boolean;
}

const sidebarItems: SidebarItem[] = [
  {
    title: "Home",
    href: "/",
    icon: Home,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
  {
    title: "Admin Dashboard",
    href: "/admin",
    icon: Shield,
    adminOnly: true,
  },
  {
    title: "Add Template",
    href: "/templates/add",
    icon: Plus,
    adminOnly: true,
  },
  {
    title: "Manage Templates",
    href: "/templates/manage",
    icon: FolderOpen,
    adminOnly: true,
  },
  {
    title: "Generate QR Code",
    href: "/qr-code",
    icon: QrCode,
    adminOnly: true,
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const [adminMode, setAdminMode] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  useEffect(() => {
    setIsHydrated(true);

    const checkAdminMode = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";
        setAdminMode(savedAdminMode);
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("adminModeChanged", handleAdminModeChange);
      return () => {
        window.removeEventListener("adminModeChanged", handleAdminModeChange);
      };
    }
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setIsMobileOpen(false);
      }
    };

    if (isMobileOpen) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isMobileOpen]);

  // Don't render during SSR
  if (!isHydrated) {
    return null;
  }

  // Filter items based on admin mode
  const visibleItems = sidebarItems.filter((item) => {
    if (item.adminOnly) {
      return adminMode;
    }
    return true;
  });

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="outline"
        size="icon"
        className="fixed top-4 left-4 z-50 lg:hidden"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? (
          <X className="h-4 w-4" />
        ) : (
          <Menu className="h-4 w-4" />
        )}
      </Button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 w-64 h-screen bg-background border-r z-40 flex flex-col transition-transform duration-300 ease-in-out shadow-lg lg:shadow-none",
          // Desktop: always visible
          "lg:translate-x-0",
          // Mobile: slide in/out based on state
          isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <Shield className="h-6 w-6 text-primary" />
            <h2 className="text-lg font-semibold">Navigation</h2>
          </div>

          <nav className="space-y-2">
            {visibleItems.map((item) => {
              const Icon = item.icon;
              const isActive =
                pathname === item.href ||
                (item.href !== "/" && pathname.startsWith(item.href)) ||
                (item.href === "/admin" &&
                  pathname.startsWith("/notifications/"));

              return (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    className={cn(
                      "w-full justify-start gap-3",
                      isActive && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => setIsMobileOpen(false)}
                  >
                    <Icon className="h-4 w-4" />
                    {item.title}
                  </Button>
                </Link>
              );
            })}
          </nav>
        </div>

        {/* Admin Mode Indicator */}
        {adminMode && (
          <div className="mt-auto p-4 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Shield className="h-3 w-3" />
              Admin Mode Active
            </div>
          </div>
        )}
      </div>
    </>
  );
}
