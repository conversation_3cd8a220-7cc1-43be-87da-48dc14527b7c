import { NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';

export async function GET() {
  try {
    const database = await getDatabase();
    
    // Get all QR codes
    const qrCodes = database.prepare('SELECT * FROM qr_codes').all();
    
    // Get archives without QR codes
    const archivesWithoutQR = database.prepare(`
      SELECT a.id, a.first_name, a.last_name 
      FROM archives a 
      LEFT JOIN qr_codes q ON a.id = q.document_id AND q.document_type = 'archive'
      WHERE q.id IS NULL
    `).all();

    return NextResponse.json({
      success: true,
      qrCodes,
      archivesWithoutQR,
      counts: {
        totalQRCodes: qrCodes.length,
        archivesWithoutQR: archivesWithoutQR.length
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
