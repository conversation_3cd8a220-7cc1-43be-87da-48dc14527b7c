import { NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';

// GET /api/qr-codes/debug - Debug QR codes in local database
export async function GET() {
  try {
    const database = await getDatabase();
    
    // Get all QR codes (active and inactive)
    const allQRCodes = database.prepare('SELECT * FROM qr_codes').all();
    
    // Get count by status
    const activeCount = database.prepare('SELECT COUNT(*) as count FROM qr_codes WHERE is_active = 1').get() as { count: number };
    const inactiveCount = database.prepare('SELECT COUNT(*) as count FROM qr_codes WHERE is_active = 0').get() as { count: number };
    
    // Get recent QR codes
    const recentQRCodes = database.prepare(`
      SELECT id, document_id, document_type, is_active, generated_at 
      FROM qr_codes 
      ORDER BY generated_at DESC 
      LIMIT 10
    `).all();

    return NextResponse.json({
      success: true,
      summary: {
        total: allQRCodes.length,
        active: activeCount.count,
        inactive: inactiveCount.count
      },
      recentQRCodes,
      allQRCodes: allQRCodes.slice(0, 5) // Show first 5 for debugging
    });
  } catch (error) {
    console.error('Error debugging QR codes:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to debug QR codes',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
