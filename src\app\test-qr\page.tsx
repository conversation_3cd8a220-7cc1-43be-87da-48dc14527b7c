"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { getClientBaseURL } from "@/lib/network-utils";

export default function TestQRPage() {
  const [networkUrl, setNetworkUrl] = useState(
    `${getClientBaseURL()}/validate/test-document-id`
  );
  const [qrCodeUrl, setQrCodeUrl] = useState("");

  useEffect(() => {
    // Get network IP for mobile accessibility
    const fetchNetworkIP = async () => {
      try {
        const response = await fetch("/api/network-ip");
        const { baseURL, success } = await response.json();
        if (success) {
          const testUrl = `${baseURL}/validate/test-document-id`;
          setNetworkUrl(testUrl);
          setQrCodeUrl(
            `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(
              testUrl
            )}`
          );
        } else {
          const fallbackUrl = `${getClientBaseURL()}/validate/test-document-id`;
          setNetworkUrl(fallbackUrl);
          setQrCodeUrl(
            `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(
              fallbackUrl
            )}`
          );
        }
      } catch (error) {
        console.error("Failed to get network IP:", error);
        const fallbackUrl = `${getClientBaseURL()}/validate/test-document-id`;
        setNetworkUrl(fallbackUrl);
        setQrCodeUrl(
          `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(
            fallbackUrl
          )}`
        );
      }
    };

    fetchNetworkIP();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>QR Code Test - New Dimensions</CardTitle>
            <p className="text-muted-foreground">
              Testing the new QR code dimensions and network URL detection
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Current Network URL:
              </h3>
              <code className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm">
                {networkUrl.replace("/validate/test-document-id", "")}
              </code>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">
                Test Validation URL:
              </h3>
              <code className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm">
                {networkUrl}
              </code>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">
                QR Code Preview (as it appears on documents):
              </h3>
              <div className="bg-white p-4 rounded-lg border inline-block">
                <div
                  style={{
                    background: "white",
                    padding: "8px",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    textAlign: "center",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                    display: "inline-block",
                    width: "76px",
                    boxSizing: "border-box",
                  }}
                >
                  <img
                    src={qrCodeUrl}
                    alt="QR Code"
                    style={{
                      width: "60px",
                      height: "60px",
                      margin: "0 auto 4px auto",
                      display: "block",
                    }}
                  />
                  <div
                    style={{
                      fontSize: "10px",
                      color: "#555",
                      fontFamily: "Arial, sans-serif",
                      fontWeight: "500",
                      lineHeight: "1.2",
                    }}
                  >
                    Scan to Validate
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                📱 Mobile Testing Instructions:
              </h4>
              <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                <li>
                  • Make sure your phone is connected to the same WiFi network
                </li>
                <li>
                  • The QR code will now use your network IP instead of
                  localhost
                </li>
                <li>• Scan the QR code above with your phone's camera</li>
                <li>
                  • It should redirect to the validation page automatically
                </li>
                <li>
                  • The test document ID won't exist, so you'll see a "not
                  found" message
                </li>
              </ul>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                ✅ Improvements Made:
              </h4>
              <ul className="text-green-700 dark:text-green-300 text-sm space-y-1">
                <li>
                  • QR code size increased from 120x120 to 200x200 pixels for
                  better scanning
                </li>
                <li>
                  • Display size increased from 60x60 to 80x80 pixels for better
                  visibility
                </li>
                <li>• Padding and styling improved for better appearance</li>
                <li>
                  • Network IP detection replaces localhost for mobile
                  compatibility
                </li>
                <li>
                  • Text changed to "Scan to Validate" for clearer instructions
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
