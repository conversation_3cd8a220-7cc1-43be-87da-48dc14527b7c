import { NextRequest, NextResponse } from 'next/server';
import { getArchivedDocument } from '@/lib/database';
import { getNetworkIP } from '@/lib/network-utils';

// GET /api/archives/[id]/pdf - Generate PDF from archived document metadata
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const archivedDoc = await getArchivedDocument(id);

    if (!archivedDoc) {
      return NextResponse.json(
        { success: false, error: 'Archived document not found' },
        { status: 404 }
      );
    }

    // Parse the stored metadata to get PDF data
    let pdfData = null;
    if (archivedDoc.metadata) {
      try {
        const metadata = JSON.parse(archivedDoc.metadata);
        pdfData = metadata.pdfData;
      } catch (parseError) {
        console.error('Error parsing archived document metadata:', parseError);
      }
    }

    if (!pdfData) {
      return NextResponse.json(
        { success: false, error: 'No document data associated with this archived document' },
        { status: 404 }
      );
    }

    // Generate QR code text using network IP for mobile accessibility
    const networkIP = getNetworkIP(request);
    const qrCodeText = `${networkIP}/validate/${id}`;

    // Generate PDF using the same template generation logic
    const response = await fetch(`${request.nextUrl.origin}/api/templates/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        templateId: pdfData.templateId,
        data: pdfData.userData,
        photoPath: null,
        addQRCode: true,
        qrCodeText,
        documentId: id, // Pass archive ID for QR code storage
        documentType: 'archive', // Specify document type
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate PDF from archived document');
    }

    const pdfBuffer = await response.arrayBuffer();

    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="archived-${archivedDoc.template_name}-${archivedDoc.applicant_name}.pdf"`,
      },
    });

  } catch (error) {
    console.error('Archive PDF generation error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}
