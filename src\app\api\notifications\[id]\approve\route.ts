import { NextRequest, NextResponse } from 'next/server';
import { archiveDocument, getDatabase } from '@/lib/database';
import { NotificationModel } from '@/lib/models/notification';
import { getCurrentAdmin } from '@/lib/admin-utils';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { approvedBy: requestApprovedBy, updatedPdfData, approvedHtmlContent } = body;

    // Get the current admin from session
    const currentAdmin = await getCurrentAdmin(request);
    const approvedBy = currentAdmin || requestApprovedBy || 'admin';

    // Get notification data from database
    const notification = await NotificationModel.findById(id);

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Use updated PDF data if provided, otherwise parse the stored PDF data
    let pdfData;
    if (updatedPdfData) {
      // Use the updated PDF data with approved status and QR code
      pdfData = updatedPdfData;
      console.log('Using updated PDF data with approved status');
    } else {
      // Fallback to original PDF data
      try {
        const pdfDataString = typeof notification.pdfData === 'string'
          ? notification.pdfData
          : JSON.stringify(notification.pdfData || {});
        pdfData = JSON.parse(pdfDataString);
      } catch (parseError) {
        console.error('Error parsing notification PDF data:', parseError);
        return NextResponse.json(
          { success: false, error: 'Invalid notification data format' },
          { status: 400 }
        );
      }
    }

    // Create complete notification data for archiving
    const userData = pdfData.userData || {};

    // Extract name fields by searching through all userData keys
    const userDataKeys = Object.keys(userData);

    // Find first name field
    const firstNameKey = userDataKeys.find(key => {
      const lowerKey = key.toLowerCase();
      return lowerKey.includes('first') && lowerKey.includes('name');
    });

    // Find last name field
    const lastNameKey = userDataKeys.find(key => {
      const lowerKey = key.toLowerCase();
      return (lowerKey.includes('last') && lowerKey.includes('name')) ||
             lowerKey.includes('surname') ||
             lowerKey === 'lastname';
    });

    // Find middle name/initial field
    const middleNameKey = userDataKeys.find(key => {
      const lowerKey = key.toLowerCase();
      return (lowerKey.includes('middle') && (lowerKey.includes('name') || lowerKey.includes('initial'))) ||
             lowerKey === 'middleinitial' ||
             lowerKey === 'middle_initial';
    });

    const firstName = firstNameKey ? userData[firstNameKey] : '';
    const lastName = lastNameKey ? userData[lastNameKey] : '';
    const middleInitial = middleNameKey ? userData[middleNameKey] : '';

    const notificationData = {
      id: id,
      title: notification.title,
      message: notification.message,
      templateName: pdfData.templateName || 'Unknown Template',
      templateId: pdfData.templateId || id,
      pdfData: pdfData,
      // Extract user data from pdfData with dynamic field detection
      firstName: firstName,
      lastName: lastName,
      middleInitial: middleInitial,
      age: userData.age || '',
      barangay: userData.barangay || '',
      ctcNumber: userData.ctcNumber || userData.ctc_number || '',
      day: userData.day || '',
      month: userData.month || '',
      year: userData.year || '',
      orNumber: userData.orNumber || userData.or_number || '',
      suffix: userData.suffix || '',
      tinNumber: userData.tinNumber || userData.tin_number || '',
      photoPath: userData.photoPath || userData.photo_path || '',
      pdfPath: userData.pdfPath || userData.pdf_path || '',
      // Additional metadata
      createdAt: notification.createdAt,
      userId: notification.userId,
      // Store approved HTML content if provided
      approvedHtmlContent: approvedHtmlContent || null,
    };

    // Debug: Log the notification data being archived
    console.log('Archiving notification data:', JSON.stringify(notificationData, null, 2));

    // Archive the document to SQLite
    const archivedDoc = await archiveDocument(notificationData, approvedBy);

    // Update QR code to point to archived document instead of notification
    try {
      const database = await getDatabase();
      const updateQRStmt = database.prepare(`
        UPDATE qr_codes
        SET document_id = ?,
            document_type = 'archive',
            validation_url = REPLACE(validation_url, '/validate/', '/validate-online/')
        WHERE document_id = ? AND document_type = 'notification'
      `);

      const qrUpdateResult = updateQRStmt.run(archivedDoc.id, id);
      if (qrUpdateResult.changes > 0) {
        console.log(`Updated ${qrUpdateResult.changes} QR code(s) to point to archived document ${archivedDoc.id}`);
      }
    } catch (qrError) {
      console.error('Failed to update QR code for archived document:', qrError);
      // Don't fail the archiving process if QR update fails
    }

    // After successful archiving, delete the notification
    const deleteSuccess = await NotificationModel.delete(id);

    if (!deleteSuccess) {
      console.warn(`Failed to delete notification ${id} after archiving, but archiving was successful`);
    }

    return NextResponse.json({
      success: true,
      message: 'Document approved, archived, and removed from notifications',
      data: archivedDoc,
      notificationDeleted: deleteSuccess
    });

  } catch (error) {
    console.error('Approve notification API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to approve and archive document' },
      { status: 500 }
    );
  }
}
